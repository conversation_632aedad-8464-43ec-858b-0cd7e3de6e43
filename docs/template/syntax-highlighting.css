/* Improved monokai color scheme for better readability */
pre { line-height: 1.4; }
span.linenos { color: #858585; background-color: transparent; padding-left: 5px; padding-right: 20px; }
.pdoc-code .hll { background-color: #404040 }
.pdoc-code { background: #2d2d30; color: #d4d4d4 }
.pdoc-code .c { color: #7c7c7c } /* Comment - softer gray */
.pdoc-code .err { color: #f48771; background-color: #2d1b1b } /* Error - softer red */
.pdoc-code .esc { color: #d4d4d4 } /* Escape */
.pdoc-code .g { color: #d4d4d4 } /* Generic */
.pdoc-code .k { color: #569cd6 } /* Keyword - softer blue */
.pdoc-code .l { color: #b5cea8 } /* Literal - softer green */
.pdoc-code .n { color: #d4d4d4 } /* Name */
.pdoc-code .o { color: #d7ba7d } /* Operator - softer yellow */
.pdoc-code .x { color: #f8f8f2 } /* Other */
.pdoc-code .p { color: #f8f8f2 } /* Punctuation */
.pdoc-code .ch { color: #75715e } /* Comment.Hashbang */
.pdoc-code .cm { color: #75715e } /* Comment.Multiline */
.pdoc-code .cp { color: #75715e } /* Comment.Preproc */
.pdoc-code .cpf { color: #75715e } /* Comment.PreprocFile */
.pdoc-code .c1 { color: #75715e } /* Comment.Single */
.pdoc-code .cs { color: #75715e } /* Comment.Special */
.pdoc-code .gd { color: #f92672 } /* Generic.Deleted */
.pdoc-code .ge { color: #f8f8f2; font-style: italic } /* Generic.Emph */
.pdoc-code .gr { color: #f8f8f2 } /* Generic.Error */
.pdoc-code .gh { color: #f8f8f2 } /* Generic.Heading */
.pdoc-code .gi { color: #a6e22e } /* Generic.Inserted */
.pdoc-code .go { color: #66d9ef } /* Generic.Output */
.pdoc-code .gp { color: #f92672; font-weight: bold } /* Generic.Prompt */
.pdoc-code .gs { color: #f8f8f2; font-weight: bold } /* Generic.Strong */
.pdoc-code .gu { color: #75715e } /* Generic.Subheading */
.pdoc-code .gt { color: #f8f8f2 } /* Generic.Traceback */
.pdoc-code .kc { color: #66d9ef } /* Keyword.Constant */
.pdoc-code .kd { color: #66d9ef } /* Keyword.Declaration */
.pdoc-code .kn { color: #f92672 } /* Keyword.Namespace */
.pdoc-code .kp { color: #66d9ef } /* Keyword.Pseudo */
.pdoc-code .kr { color: #66d9ef } /* Keyword.Reserved */
.pdoc-code .kt { color: #66d9ef } /* Keyword.Type */
.pdoc-code .ld { color: #e6db74 } /* Literal.Date */
.pdoc-code .m { color: #ae81ff } /* Literal.Number */
.pdoc-code .s { color: #ce9178 } /* Literal.String - softer orange */
.pdoc-code .na { color: #9cdcfe } /* Name.Attribute - softer cyan */
.pdoc-code .nb { color: #d4d4d4 } /* Name.Builtin */
.pdoc-code .nc { color: #4ec9b0 } /* Name.Class - softer teal */
.pdoc-code .no { color: #569cd6 } /* Name.Constant - softer blue */
.pdoc-code .nd { color: #dcdcaa } /* Name.Decorator - softer yellow */
.pdoc-code .ni { color: #f8f8f2 } /* Name.Entity */
.pdoc-code .ne { color: #a6e22e } /* Name.Exception */
.pdoc-code .nf { color: #dcdcaa } /* Name.Function - softer yellow */
.pdoc-code .nl { color: #f8f8f2 } /* Name.Label */
.pdoc-code .nn { color: #f8f8f2 } /* Name.Namespace */
.pdoc-code .nx { color: #a6e22e } /* Name.Other */
.pdoc-code .py { color: #f8f8f2 } /* Name.Property */
.pdoc-code .nt { color: #f92672 } /* Name.Tag */
.pdoc-code .nv { color: #f8f8f2 } /* Name.Variable */
.pdoc-code .ow { color: #f92672 } /* Operator.Word */
.pdoc-code .w { color: #f8f8f2 } /* Text.Whitespace */
.pdoc-code .mb { color: #ae81ff } /* Literal.Number.Bin */
.pdoc-code .mf { color: #ae81ff } /* Literal.Number.Float */
.pdoc-code .mh { color: #ae81ff } /* Literal.Number.Hex */
.pdoc-code .mi { color: #ae81ff } /* Literal.Number.Integer */
.pdoc-code .mo { color: #ae81ff } /* Literal.Number.Oct */
.pdoc-code .sa { color: #e6db74 } /* Literal.String.Affix */
.pdoc-code .sb { color: #e6db74 } /* Literal.String.Backtick */
.pdoc-code .sc { color: #e6db74 } /* Literal.String.Char */
.pdoc-code .dl { color: #e6db74 } /* Literal.String.Delimiter */
.pdoc-code .sd { color: #e6db74 } /* Literal.String.Doc */
.pdoc-code .s2 { color: #e6db74 } /* Literal.String.Double */
.pdoc-code .se { color: #ae81ff } /* Literal.String.Escape */
.pdoc-code .sh { color: #e6db74 } /* Literal.String.Heredoc */
.pdoc-code .si { color: #e6db74 } /* Literal.String.Interpol */
.pdoc-code .sx { color: #e6db74 } /* Literal.String.Other */
.pdoc-code .sr { color: #e6db74 } /* Literal.String.Regex */
.pdoc-code .s1 { color: #e6db74 } /* Literal.String.Single */
.pdoc-code .ss { color: #e6db74 } /* Literal.String.Symbol */
.pdoc-code .bp { color: #f8f8f2 } /* Name.Builtin.Pseudo */
.pdoc-code .fm { color: #a6e22e } /* Name.Function.Magic */
.pdoc-code .vc { color: #f8f8f2 } /* Name.Variable.Class */
.pdoc-code .vg { color: #f8f8f2 } /* Name.Variable.Global */
.pdoc-code .vi { color: #f8f8f2 } /* Name.Variable.Instance */
.pdoc-code .vm { color: #f8f8f2 } /* Name.Variable.Magic */
