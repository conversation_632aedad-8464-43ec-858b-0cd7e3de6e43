:root {
    --pdoc-background: #1a1b1e;
}

.pdoc {
    /* Improved text colors for better readability */
    --text: #d4d4d4;
    --muted: #9d9d9d;
    --link: #4fc3f7;
    --link-hover: #29b6f6;
    --code: #252526;
    --active: #404040;

    /* Softer accent colors */
    --accent: #2d2d30;
    --accent2: #3e3e42;

    --nav-hover: rgba(0, 0, 0, 0.1);
    --name: #9cdcfe;
    --def: #4ec9b0;
    --annotation: #4ec9b0;

    /* Improved typography for readability */
    line-height: 1.6;
    font-weight: 400;
}

.pdoc-code {
    margin-left: 0em;
    margin-right: 0em;
}

/* Improved readability adjustments */
.pdoc {
    /* Better font rendering for dark themes */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Improved heading contrast and spacing */
.pdoc h1, .pdoc h2, .pdoc h3, .pdoc h4, .pdoc h5, .pdoc h6 {
    color: #f0f0f0;
    font-weight: 500;
    letter-spacing: -0.01em;
}

/* Better code block readability */
.pdoc pre {
    background-color: var(--code);
    border-radius: 6px;
    border: 1px solid var(--accent2);
}

/* Improved inline code readability */
.pdoc code {
    background-color: var(--accent);
    color: #e6e6e6;
    font-weight: 500;
}

img, svg {
    vertical-align: middle;
    width: 100%;
}

main {
    margin: auto;
}
nav {
    left: max(calc(50% - var(--sidebar-width)/2 - 26rem), 0px)
}
